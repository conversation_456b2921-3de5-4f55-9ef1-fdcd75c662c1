'use client';

import { useEffect } from 'react';

export default function TestDrawflow() {
  useEffect(() => {
    const testImport = async () => {
      try {
        console.log('Testing drawflow import...');
        const Drawflow = await import('drawflow');
        console.log('Drawflow imported successfully:', Drawflow);
        console.log('Drawflow default:', Drawflow.default);
      } catch (error) {
        console.error('Failed to import drawflow:', error);
      }
    };
    
    testImport();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Drawflow Import Test</h1>
      <p>Check the console for import results.</p>
    </div>
  );
}
