{"name": "drawflow", "version": "0.0.62", "description": "Simple flow library", "main": "dist/drawflow.min.js", "scripts": {"dev": "webpack-dev-server --mode development --open --host 0.0.0.0", "build": "webpack --mode=production && gulp", "build:patch": "npm version patch && npm run build && npm run update-react-demo", "build:minor": "npm version minor && npm run build && npm run update-react-demo", "build:major": "npm version major && npm run build && npm run update-react-demo", "update-react-demo": "node scripts/update-react-demo.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/jerosoler/Drawflow.git"}, "keywords": ["flow", "javascript", "javascript-library", "flow-based-programming", "flowchart"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jerosoler/Drawflow/issues"}, "homepage": "https://github.com/jerosoler/Drawflow#readme", "devDependencies": {"babel-core": "^6.26.3", "babel-loader": "^8.1.0", "babel-polyfill": "^6.26.0", "babel-preset-es2015": "^6.24.1", "css-loader": "^3.5.2", "es-dev-server": "^2.1.0", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-minify-css": "^1.2.4", "gulp-replace": "^1.0.0", "mini-css-extract-plugin": "^0.9.0", "webpack": "^4.42.1", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.11.2"}}