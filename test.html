<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>
    <script src="drawflow.min.js"></script>
    <link rel="stylesheet" type="text/css" href="src/drawflow.css" />
    <style>
      #drawflow {
        display: block;
        position:relative;
        width: 100%;
        height: 800px;
        left: 50px;
        border: 1px solid red;
      }


    </style>
</head>
<body>
  <div class="but" onclick="editor.addModule('nameNewModule');editor.changeModule('nameNewModule');">Tes module</div>
  <div class="but" onclick="importa()">Import1</div>
  <div class="but" onclick="import2()">Import2</div>
  <div class="but" onclick="export2()">Export</div>
  <div class="but" onclick="importexport()">ImportFromExport</div>
  <div class="but" onclick="editor.clear()">Clear</div>
  <div id="drawflow"></div>
  <script>

  var id = document.getElementById("drawflow");

  const editor = new Drawflow(id);

  const config = { attributes: true, childList: true, subtree: true };

    const callback = function(mutationsList, observer) {
      // Use traditional 'for loops' for IE 11
      for(const mutation of mutationsList) {
          if (mutation.type === 'childList') {
              if(mutation.addedNodes[0]) {
                if(mutation.addedNodes[0].nodeName === "svg" && mutation.addedNodes[0].classList[0] === "connection") {
                  if(mutation.addedNodes[0].classList.length > 4) {
                  console.log('A child node has been added or removed.');
                    const node_id = parseInt(mutation.addedNodes[0].classList[2].slice(14));
                    const info_node = editor.getNodeFromId(node_id);
                    mutation.addedNodes[0].classList.add(info_node.class);
                   //console.log(mutation);
                  }
                }

              }
          }
          else if (mutation.type === 'attributes') {
              if(mutation.attributeName === "class") {
                if(mutation.target.nodeName === "svg" && mutation.target.classList[0] === "connection") {
                  if(mutation.target.classList.length > 4 && mutation.target.classList.length < 6) {
                      //console.log("add class");
                      const node_id = parseInt(mutation.target.classList[2].slice(14));
                      const info_node = editor.getNodeFromId(node_id);
                      console.log(info_node);
                      mutation.target.classList.add(info_node.class);
                }
              }
          }
      }
    }
  };
  const observer = new MutationObserver(callback);
  observer.observe(id, config);

  editor.reroute = true;
  editor.reroute_fix_curvature = true;

  editor.start();

    var html = '<div><input type="text" df-name></div>';
  var data = { "name": 'hola' };


  var testNode = document.createElement("div");
  testNode.innerHTML = 'Hola caracola <input type="text" df-name>';

  /*const datatest = {
          level0: 'test0',
          level1: {
           name1: 'TEST 1',
           level2: {
             name2: 'test 2',
             level3: {
               name3: "TESTTTTT 3"
             }
           }
          }
         }
         const htmltest = `
         <input type="text" df-level0>
         <input type="text" df-level1-name1>
         <input type="text" df-level1-level2-name2>
         <input type="text" df-level1-level2-level3-name3>
         `;
         editor.addNode('test2', 1,1, 100, 200, 'test2', datatest, htmltest );


  editor.registerNode("testNode", testNode);


  //editor.addNode('github', 0, 1, 150, 300, 'github', data, html);

  editor.addNode('github', 1, 2, 400, 250, 'github', data, "testNode", true);


  editor.addNode('github', 1, 1, 400, 375, 'github', data, "<b>Holaaa</b>");
  editor.addNode('github', 0, 1, 100, 400, 'github', data, "<b>Adios</b>");

  editor.addNode('github', 0, 1, 0, 375, 'github', data, '<div>Hey<div id="test" contenteditable="true" df-name></div></div>');

  //editor.import({"drawflow":{"Home":{"data":{"1":{"id":1,"name":"github","data":{"name":""},"class":"github","html":"\n  <div><input type=\"text\" df-name></div>\n  ","typenode":false,"inputs":{},"outputs":{"output_1":{"connections":[]}},"pos_x":122,"pos_y":191},"2":{"id":2,"name":"github","data":{"name":""},"class":"github","html":"testNode","typenode":true,"inputs":{},"outputs":{"output_1":{"connections":[]}},"pos_x":190,"pos_y":334}}}}});


  editor.addNode('github', 1, 1, 0, 375, 'custom-node-1', data, "<b>Holaaa</b>");
  editor.addNode('github', 1, 2, 500, 175, 'custom-node-2', data, "<b>Holaaa</b>");
  editor.addNode('github', 1, 1, 300, 375, 'custom-node-3', data, "<b>Holaaa</b>");
  editor.addNode('github', 1, 1, 0, 375, 'custom-node-4', data, "<b>Holaaa</b>");

 editor.addConnection(1,2,'output_1','input_1');

  editor.addNodeInput(1);
  editor.addNodeOutput(1);

  editor.removeNodeOutput(1, 'output_2');
  editor.removeNodeInput(1, 'input_2');


  const datatest2 = {
          level0: 'test0123132',
          level1: {
           name1: 'TEST 11231',
           level2: {
             name2: 'test 2211321',
             level3: {
               name3: "TESTTTTT 312313"
             }
           }
          }
         }
editor.updateNodeDataFromId(1, datatest2);
  editor.addConnection(6,7,'output_1', 'input_1');

  editor.removeSingleConnection(6,7,'output_1', 'input_1'); */

    var exportvalue = null;
    function export2(){
      exportvalue = editor.export();
    }
    function importexport() {
      editor.import(exportvalue);
    }
    function importa() {
      console.log(caracola);
      editor.import(caracola);
    }
    function import2() {
      editor.import(caracola2);
    }

    editor.on("nodeSelected", function(id) {
      console.log("node selected "+ id)
    })

    editor.on("nodeCreated", function(id) {
      console.log("node created "+ id)
    })

    editor.on("connectionCreated", function(id) {
      console.log("node created "+ id)
    })

    editor.on("import", function() {
      console.log("import");
    })

    editor.on("rerouteMoved", function(id) {
      console.log("Moved reroute " + id);
    })


    editor.on("nodeDataChanged", function(data) {
      console.log("Edit Data");
      console.log(data);

    })


    var caracola = {"drawflow":{"Home":{"data":{"1":{"id":1,"name":"welcome","data":{},"class":"welcome","html":"\n    <div>\n      <div class=\"title-box\">👏 Welcome!!</div>\n      <div class=\"box\">\n        <p>Simple flow library <b>demo</b>\n        <a href=\"https://github.com/jerosoler/Drawflow\" target=\"_blank\">Drawflow</a> by <b>Jero Soler</b></p><br>\n\n        <p>Multiple input / outputs<br>\n           Data sync nodes<br>\n           Import / export<br>\n           Modules support<br>\n           Simple use<br>\n           Type: Fixed or Edit<br>\n           Events: view console<br>\n           Pure Javascript<br>\n        </p>\n        <br>\n        <p><b><u>Shortkeys:</u></b></p>\n        <p>🎹 <b>Delete</b> for remove selected<br>\n        💠 Mouse Left Click == Move<br>\n        ❌ Mouse Right == Delete Option<br>\n        🔍 Ctrl + Wheel == Zoom<br>\n        📱 Mobile support<br>\n        ...</p>\n      </div>\n    </div>\n    ", "typenode": false, "inputs":{},"outputs":{},"pos_x":50,"pos_y":50},"2":{"id":2,"name":"slack","data":{},"class":"slack","html":"\n          <div>\n            <div class=\"title-box\"><i class=\"fab fa-slack\"></i> Slack chat message</div>\n          </div>\n          ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"7","input":"output_1"}]}},"outputs":{},"pos_x":1028,"pos_y":87},"3":{"id":3,"name":"telegram","data":{"channel":"channel_2"},"class":"telegram","html":"\n          <div>\n            <div class=\"title-box\"><i class=\"fab fa-telegram-plane\"></i> Telegram bot</div>\n            <div class=\"box\">\n              <p>Send to telegram</p>\n              <p>select channel</p>\n              <select df-channel>\n                <option value=\"channel_1\">Channel 1</option>\n                <option value=\"channel_2\">Channel 2</option>\n                <option value=\"channel_3\">Channel 3</option>\n                <option value=\"channel_4\">Channel 4</option>\n              </select>\n            </div>\n          </div>\n          ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"7","input":"output_1"}]}},"outputs":{},"pos_x":1032,"pos_y":184},"4":{"id":4,"name":"email","data":{},"class":"email","html":"\n            <div>\n              <div class=\"title-box\"><i class=\"fas fa-at\"></i> Send Email </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"5","input":"output_1"}]}},"outputs":{},"pos_x":1033,"pos_y":439},"5":{"id":5,"name":"template","data":{"template":"Write your template"},"class":"template","html":"\n            <div>\n              <div class=\"title-box\"><i class=\"fas fa-code\"></i> Template</div>\n              <div class=\"box\">\n                Ger Vars\n                <textarea df-template></textarea>\n                Output template with vars\n              </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"6","input":"output_1"}]}},"outputs":{"output_1":{"connections":[{"node":"4","output":"input_1"},{"node":"11","output":"input_1"}]}},"pos_x":607,"pos_y":304},"6":{"id":6,"name":"github","data":{"name":"https://github.com/jerosoler/Drawflow"},"class":"github","html":"\n          <div>\n            <div class=\"title-box\"><i class=\"fab fa-github \"></i> Github Stars</div>\n            <div class=\"box\">\n              <p>Enter repository url</p>\n            <input type=\"text\" df-name>\n            </div>\n          </div>\n          ", "typenode": false, "inputs":{},"outputs":{"output_1":{"connections":[{"node":"5","output":"input_1"}]}},"pos_x":341,"pos_y":191},"7":{"id":7,"name":"facebook","data":{},"class":"facebook","html":"\n        <div>\n          <div class=\"title-box\"><i class=\"fab fa-facebook\"></i> Facebook Message</div>\n        </div>\n        ", "typenode": false, "inputs":{},"outputs":{"output_1":{"connections":[{"node":"2","output":"input_1"},{"node":"3","output":"input_1"},{"node":"11","output":"input_1"}]}},"pos_x":347,"pos_y":87},"11":{"id":11,"name":"log","data":{},"class":"log","html":"\n            <div>\n              <div class=\"title-box\"><i class=\"fas fa-file-signature\"></i> Save log file </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"5","input":"output_1"},{"node":"7","input":"output_1"}]}},"outputs":{},"pos_x":1031,"pos_y":363}}},"Other":{"data":{"8":{"id":8,"name":"personalized","data":{},"class":"personalized","html":"\n            <div>\n              Personalized\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"12","input":"output_1"},{"node":"12","input":"output_2"},{"node":"12","input":"output_3"},{"node":"12","input":"output_4"}]}},"outputs":{"output_1":{"connections":[{"node":"9","output":"input_1"}]}},"pos_x":764,"pos_y":227},"9":{"id":9,"name":"dbclick","data":{"name":"Hello World!!"},"class":"dbclick","html":"\n            <div>\n            <div class=\"title-box\"><i class=\"fas fa-mouse\"></i> Db Click</div>\n              <div class=\"box dbclickbox\" ondblclick=\"showpopup(event)\">\n                Db Click here\n                <div class=\"modal\" style=\"display:none\">\n                  <div class=\"modal-content\">\n                    <span class=\"close\" onclick=\"closemodal(event)\">&times;</span>\n                    Change your variable {name} !\n                    <input type=\"text\" df-name>\n                  </div>\n\n                </div>\n              </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"8","input":"output_1"}]}},"outputs":{"output_1":{"connections":[{"node":"12","output":"input_2"}]}},"pos_x":209,"pos_y":38},"12":{"id":12,"name":"multiple","data":{},"class":"multiple","html":"\n            <div>\n              <div class=\"box\">\n                Multiple!\n              </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[]},"input_2":{"connections":[{"node":"9","input":"output_1"}]},"input_3":{"connections":[]}},"outputs":{"output_1":{"connections":[{"node":"8","output":"input_1"}]},"output_2":{"connections":[{"node":"8","output":"input_1"}]},"output_3":{"connections":[{"node":"8","output":"input_1"}]},"output_4":{"connections":[{"node":"8","output":"input_1"}]}},"pos_x":179,"pos_y":272}}}}}
    //editor.import(caracola);


    var caracola2 = {"drawflow":{"Home":{"data":{"1":{"id":1,"name":"welcome","data":{},"class":"welcome","html":"\n    <div>\n      <div class=\"title-box\">👏 Welcome!!</div>\n      <div class=\"box\">\n        <p>Simple flow library <b>demo</b>\n        <a href=\"https://github.com/jerosoler/Drawflow\" target=\"_blank\">Drawflow</a> by <b>Jero Soler</b></p><br>\n\n        <p>Multiple input / outputs<br>\n           Data sync nodes<br>\n           Import / export<br>\n           Modules support<br>\n           Simple use<br>\n           Type: Fixed or Edit<br>\n           Events: view console<br>\n           Pure Javascript<br>\n        </p>\n        <br>\n        <p><b><u>Shortkeys:</u></b></p>\n        <p>🎹 <b>Delete</b> for remove selected<br>\n        💠 Mouse Left Click == Move<br>\n        ❌ Mouse Right == Delete Option<br>\n        🔍 Ctrl + Wheel == Zoom<br>\n        📱 Mobile support<br>\n        ...</p>\n      </div>\n    </div>\n    ", "typenode": false, "inputs":{},"outputs":{},"pos_x":50,"pos_y":50},"2":{"id":2,"name":"slack","data":{},"class":"slack","html":"\n          <div>\n            <div class=\"title-box\"><i class=\"fab fa-slack\"></i> Slack chat message</div>\n          </div>\n          ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"7","input":"output_1"}]}},"outputs":"aaa","pos_x":"lala","pos_y":87},"3":{"id":3,"name":"telegram","data":{"channel":"channel_2"},"class":"telegram","html":"\n          <div>\n            <div class=\"title-box\"><i class=\"fab fa-telegram-plane\"></i> Telegram bot</div>\n            <div class=\"box\">\n              <p>Send to telegram</p>\n              <p>select channel</p>\n              <select df-channel>\n                <option value=\"channel_1\">Channel 1</option>\n                <option value=\"channel_2\">Channel 2</option>\n                <option value=\"channel_3\">Channel 3</option>\n                <option value=\"channel_4\">Channel 4</option>\n              </select>\n            </div>\n          </div>\n          ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"7","input":"output_1"}]}},"outputs":{},"pos_x":1032,"pos_y":184},"4":{"id":4,"name":"email","data":{},"class":"email","html":"\n            <div>\n              <div class=\"title-box\"><i class=\"fas fa-at\"></i> Send Email </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"5","input":"output_1"}]}},"outputs":{},"pos_x":1033,"pos_y":439},"5":{"id":5,"name":"template","data":{"template":"Write your template"},"class":"template","html":"\n            <div>\n              <div class=\"title-box\"><i class=\"fas fa-code\"></i> Template</div>\n              <div class=\"box\">\n                Ger Vars\n                <textarea df-template></textarea>\n                Output template with vars\n              </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"6","input":"output_1"}]}},"outputs":{"output_1":{"connections":[{"node":"4","output":"input_1"},{"node":"11","output":"input_1"}]}},"pos_x":607,"pos_y":304},"6":{"id":6,"name":"github","data":{"name":"https://github.com/jerosoler/Drawflow"},"class":"github","html":"\n          <div>\n            <div class=\"title-box\"><i class=\"fab fa-github \"></i> Github Stars</div>\n            <div class=\"box\">\n              <p>Enter repository url</p>\n            <input type=\"text\" df-name>\n            </div>\n          </div>\n          ", "typenode": false, "inputs":{},"outputs":{"output_1":{"connections":[{"node":"5","output":"input_1"}]}},"pos_x":341,"pos_y":191},"7":{"id":7,"name":"facebook","data":{},"class":"facebook","html":"\n        <div>\n          <div class=\"title-box\"><i class=\"fab fa-facebook\"></i> Facebook Message</div>\n        </div>\n        ", "typenode": false, "inputs":{},"outputs":{"output_1":{"connections":[{"node":"2","output":"input_1"},{"node":"3","output":"input_1"},{"node":"11","output":"input_1"}]}},"pos_x":347,"pos_y":87},"11":{"id":11,"name":"log","data":{},"class":"log","html":"\n            <div>\n              <div class=\"title-box\"><i class=\"fas fa-file-signature\"></i> Save log file </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"5","input":"output_1"},{"node":"7","input":"output_1"}]}},"outputs":{},"pos_x":1031,"pos_y":363}}},"Other":{"data":{"8":{"id":8,"name":"personalized","data":{},"class":"personalized","html":"\n            <div>\n              Personalized\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"12","input":"output_1"},{"node":"12","input":"output_2"},{"node":"12","input":"output_3"},{"node":"12","input":"output_4"}]}},"outputs":{"output_1":{"connections":[{"node":"9","output":"input_1"}]}},"pos_x":764,"pos_y":227},"9":{"id":9,"name":"dbclick","data":{"name":"Hello World!!"},"class":"dbclick","html":"\n            <div>\n            <div class=\"title-box\"><i class=\"fas fa-mouse\"></i> Db Click</div>\n              <div class=\"box dbclickbox\" ondblclick=\"showpopup(event)\">\n                Db Click here\n                <div class=\"modal\" style=\"display:none\">\n                  <div class=\"modal-content\">\n                    <span class=\"close\" onclick=\"closemodal(event)\">&times;</span>\n                    Change your variable {name} !\n                    <input type=\"text\" df-name>\n                  </div>\n\n                </div>\n              </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[{"node":"8","input":"output_1"}]}},"outputs":{"output_1":{"connections":[{"node":"12","output":"input_2"}]}},"pos_x":209,"pos_y":38},"12":{"id":12,"name":"multiple","data":{},"class":"multiple","html":"\n            <div>\n              <div class=\"box\">\n                Multiple!\n              </div>\n            </div>\n            ", "typenode": false, "inputs":{"input_1":{"connections":[]},"input_2":{"connections":[{"node":"9","input":"output_1"}]},"input_3":{"connections":[]}},"outputs":{"output_1":{"connections":[{"node":"8","output":"input_1"}]},"output_2":{"connections":[{"node":"8","output":"input_1"}]},"output_3":{"connections":[{"node":"8","output":"input_1"}]},"output_4":{"connections":[{"node":"8","output":"input_1"}]}},"pos_x":"lala","pos_y":272}}}}};

    /*setInterval(function(){
    console.log(caracola.drawflow.Home.data);
  }, 200);*/
/*
    setTimeout(function() {
      console.log("Exportado");
      exportdata = editor.export();
      console.log(exportdata);

      console.log(exportdata);

      console.log(exportdata.drawflow['Home'].data);
      setTimeout(function() {
        editor.clearModuleSelected();
        console.log("CLEAR");
        console.log(exportdata.drawflow['Home'].data);
        setTimeout(function() {
          console.log("Importado");
          console.log(exportdata.drawflow['Home'].data);

        editor.import(exportdata);
      }, 2000);
    }, 2000);
  }, 3000);

*/



  editor.import(caracola);
  //console.log(editor.drawflow);
  //console.log(editor.export());
  var data = { "name": 'hola', "name2": { "name3": "Hola caracola "} };
  editor.addNode('github', 0, 1, 100, 500, 'github', data, '<div>Hey<input type="text"  df-name></div>');
  editor.addNode('github', 0, 1, 0, 375, 'github', data, '<div>Hey<div id="test" contenteditable="true" df-name2-name3></div></div>');
  </script>



<style>
.connection.github.output_1 path {
  stroke: red;
}

.connection.github.output_2 path {
  stroke: green;
}
</style>
</body>
</html>
