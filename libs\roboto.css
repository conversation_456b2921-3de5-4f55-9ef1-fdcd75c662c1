/* Roboto Font - Local fallback to system fonts */
/* Using system fonts that closely match Roboto */

/* Define Roboto font family with system font fallbacks */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  src: local('Segoe UI Light'), local('Helvetica Neue Light'), local('Arial');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  src: local('Segoe UI'), local('Helvetica Neue'), local('Arial');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  src: local('Segoe UI Semibold'), local('Helvetica Neue Medium'), local('Arial');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  src: local('Segoe UI Bold'), local('Helvetica Neue Bold'), local('Arial Bold');
}

/* Apply Roboto font family with comprehensive fallbacks */
body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}
