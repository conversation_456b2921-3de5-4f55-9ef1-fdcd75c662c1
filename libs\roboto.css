/* Roboto Font - 使用微软雅黑作为中文字体后备 */
/* 由于网络问题无法下载真正的 Roboto 字体文件，使用系统字体作为后备 */
/* 特别针对中文环境优化，使用微软雅黑作为主要中文字体 */

/* Roboto Light (300) - 使用系统轻字体，中文使用微软雅黑 */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local('Microsoft YaHei Light'),
       local('微软雅黑 Light'),
       local('Segoe UI Light'),
       local('Helvetica Neue Light'),
       local('Microsoft YaHei'),
       local('微软雅黑'),
       local('SimHei'),
       local('黑体'),
       local('Arial'),
       local('sans-serif');
}

/* Roboto Regular (400) - 使用系统常规字体，中文使用微软雅黑 */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Microsoft YaHei'),
       local('微软雅黑'),
       local('Segoe UI'),
       local('Helvetica Neue'),
       local('SimHei'),
       local('黑体'),
       local('Arial'),
       local('sans-serif');
}

/* Roboto Medium (500) - 使用系统中等字体，中文使用微软雅黑 */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local('Microsoft YaHei Bold'),
       local('微软雅黑 Bold'),
       local('Segoe UI Semibold'),
       local('Helvetica Neue Medium'),
       local('Microsoft YaHei'),
       local('微软雅黑'),
       local('SimHei'),
       local('黑体'),
       local('Arial'),
       local('sans-serif');
}

/* Roboto Bold (700) - 使用系统粗体字体，中文使用微软雅黑粗体 */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local('Microsoft YaHei Bold'),
       local('微软雅黑 Bold'),
       local('Segoe UI Bold'),
       local('Helvetica Neue Bold'),
       local('Microsoft YaHei'),
       local('微软雅黑'),
       local('SimHei'),
       local('黑体'),
       local('Arial Bold'),
       local('Arial'),
       local('sans-serif');
}

/* 应用 Roboto 字体族，优先使用微软雅黑作为中文字体 */
body {
  font-family: 'Roboto',
               'Microsoft YaHei',
               '微软雅黑',
               -apple-system,
               BlinkMacSystemFont,
               'Segoe UI',
               'Helvetica Neue',
               'SimHei',
               '黑体',
               Helvetica,
               Arial,
               sans-serif;
}
