import DrawflowDemo from '@/components/DrawflowDemo';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Drawflow Next.js Demo</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Visual Flow Editor</span>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">
              Interactive Flow Editor
            </h2>
            <p className="text-gray-600">
              Create and edit visual flows with drag-and-drop functionality.
              This demo shows Drawflow integrated with Next.js and React.
            </p>
          </div>

          {/* Drawflow 编辑器 */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <DrawflowDemo />
          </div>

          {/* 说明文档 */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">🎯 Features</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Drag and drop nodes</li>
                <li>• Connect nodes with lines</li>
                <li>• Interactive editing</li>
                <li>• Responsive design</li>
              </ul>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">🚀 Technology</h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Next.js 15</li>
                <li>• React 18</li>
                <li>• TypeScript</li>
                <li>• Drawflow Library</li>
              </ul>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">💡 Usage</h3>
              <ul className="text-sm text-purple-800 space-y-1">
                <li>• Click and drag nodes</li>
                <li>• Connect output to input</li>
                <li>• Right-click for options</li>
                <li>• Zoom with mouse wheel</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
