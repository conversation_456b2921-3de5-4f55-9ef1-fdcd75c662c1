'use client';

import { useEffect, useRef } from 'react';
import Drawflow from 'drawflow';

interface DrawflowEditorProps {
  className?: string;
}

const DrawflowEditor: React.FC<DrawflowEditorProps> = ({ className = '' }) => {
  const drawflowRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<Drawflow | null>(null);

  useEffect(() => {
    if (!drawflowRef.current) return;

    // 初始化 Drawflow
    const editor = new Drawflow(drawflowRef.current);
    editorRef.current = editor;

    editor.start();

    // 添加一些示例节点
    const html = `
      <div>
        <div class="title-box">👤 Welcome</div>
        <div class="box">
          <p>Start your flow here</p>
        </div>
      </div>
    `;

    const html2 = `
      <div>
        <div class="title-box">📧 Email</div>
        <div class="box">
          <p>Send notification</p>
        </div>
      </div>
    `;

    const html3 = `
      <div>
        <div class="title-box">✅ Success</div>
        <div class="box">
          <p>Process completed</p>
        </div>
      </div>
    `;

    const html4 = `
      <div>
        <div class="title-box">🔄 Process</div>
        <div class="box">
          <p>Data processing</p>
        </div>
      </div>
    `;

    // 添加节点
    editor.addNode('welcome', 1, 1, 150, 200, 'welcome', {}, html);
    editor.addNode('process', 1, 1, 400, 150, 'process', {}, html4);
    editor.addNode('email', 1, 1, 650, 100, 'email', {}, html2);
    editor.addNode('success', 0, 0, 650, 250, 'success', {}, html3);

    // 连接节点
    editor.addConnection(1, 2, "output_1", "input_1");
    editor.addConnection(2, 3, "output_1", "input_1");
    editor.addConnection(2, 4, "output_1", "input_1");

    // 清理函数
    return () => {
      if (editorRef.current) {
        editorRef.current.clear();
      }
    };
  }, []);

  return (
    <div className={`w-full h-full ${className}`}>
      <div
        ref={drawflowRef}
        className="w-full h-full bg-gray-50 border border-gray-300 rounded-lg"
        style={{ minHeight: '600px' }}
      />
    </div>
  );
};

export default DrawflowEditor;
