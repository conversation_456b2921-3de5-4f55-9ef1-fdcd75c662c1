'use client';

import { useEffect, useRef } from 'react';
import Drawflow from 'drawflow';

interface DrawflowEditorProps {
  className?: string;
}

const DrawflowEditor: React.FC<DrawflowEditorProps> = ({ className = '' }) => {
  const drawflowRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<Drawflow | null>(null);

  useEffect(() => {
    if (!drawflowRef.current) return;

    // 初始化 Drawflow
    const editor = new Drawflow(drawflowRef.current);
    editorRef.current = editor;

    editor.start();

    // 使用 setTimeout 确保 DOM 完全渲染后再添加节点和连接
    setTimeout(() => {
      // 添加一些示例节点
      const html = `
        <div>
          <div class="title-box">👤 Welcome</div>
          <div class="box">
            <p>Start your flow here</p>
          </div>
        </div>
      `;

      const html2 = `
        <div>
          <div class="title-box">📧 Email</div>
          <div class="box">
            <p>Send notification</p>
          </div>
        </div>
      `;

      const html3 = `
        <div>
          <div class="title-box">✅ Success</div>
          <div class="box">
            <p>Process completed</p>
          </div>
        </div>
      `;

      const html4 = `
        <div>
          <div class="title-box">🔄 Process</div>
          <div class="box">
            <p>Data processing</p>
          </div>
        </div>
      `;

      try {
        // 添加节点
        editor.addNode('welcome', 1, 1, 150, 200, 'welcome', {}, html);
        editor.addNode('process', 1, 1, 400, 150, 'process', {}, html4);
        editor.addNode('email', 1, 1, 650, 100, 'email', {}, html2);
        editor.addNode('success', 0, 0, 650, 250, 'success', {}, html3);

        console.log('节点添加成功');
      } catch (nodeError) {
        console.warn('添加节点时出错:', nodeError);
      }
    }, 100);

    // 清理函数
    return () => {
      if (editorRef.current) {
        try {
          editorRef.current.clear();
        } catch (error) {
          console.warn('清理 Drawflow 时出错:', error);
        }
      }
    };
  }, []);

  return (
    <div className={`w-full h-full ${className}`}>
      <div
        ref={drawflowRef}
        className="w-full h-full bg-gray-50 border border-gray-300 rounded-lg"
        style={{ minHeight: '600px' }}
      />
    </div>
  );
};

export default DrawflowEditor;
