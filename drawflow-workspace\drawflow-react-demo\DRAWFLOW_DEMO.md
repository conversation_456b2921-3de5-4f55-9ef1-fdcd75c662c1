# Drawflow Next.js Demo

这是一个使用 Next.js 15 和 React 18 集成 Drawflow 可视化流程编辑器的演示项目。

## 🚀 功能特性

- ✅ Next.js 15 最新版本
- ✅ React 18 + TypeScript
- ✅ Tailwind CSS 样式
- ✅ Drawflow 可视化流程编辑器
- ✅ 拖拽节点功能
- ✅ 节点连接功能
- ✅ 响应式设计
- ✅ 服务端渲染 (SSR) 兼容

## 📦 安装依赖

```bash
npm install
```

## 🏃‍♂️ 运行项目

```bash
npm run dev
```

然后在浏览器中打开 [http://localhost:3000](http://localhost:3000)

## 🎯 项目结构

```
drawflow-nextjs-demo/
├── src/
│   ├── app/
│   │   ├── globals.css          # 全局样式
│   │   ├── layout.tsx           # 布局组件
│   │   └── page.tsx             # 主页面
│   └── components/
│       ├── DrawflowDemo.tsx     # Drawflow 演示组件
│       ├── DrawflowEditor.tsx   # Drawflow 编辑器组件
│       └── drawflow.css         # Drawflow 自定义样式
├── package.json
└── README.md
```

## 🔧 技术栈

- **Next.js 15** - React 全栈框架
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Drawflow** - 可视化流程编辑器库

## 💡 使用说明

1. **拖拽节点**: 点击并拖拽节点到任意位置
2. **连接节点**: 从输出点拖拽到输入点创建连接
3. **选择节点**: 点击节点进行选择
4. **缩放视图**: 使用鼠标滚轮缩放画布
5. **右键菜单**: 右键点击获取更多选项

## 🎨 自定义样式

项目包含了自定义的 Drawflow 样式，位于 `src/components/drawflow.css`：

- 现代化的节点设计
- 自定义连接线样式
- 响应式布局支持
- 深色模式兼容

## 📝 开发注意事项

1. **SSR 兼容性**: 使用 `dynamic` 导入和 `ssr: false` 确保 Drawflow 在客户端渲染
2. **类型安全**: 所有组件都使用 TypeScript 编写
3. **性能优化**: 使用 React 的 `useRef` 和 `useEffect` 管理 Drawflow 实例

## 🔗 相关链接

- [Next.js 文档](https://nextjs.org/docs)
- [Drawflow 文档](https://github.com/jerosoler/Drawflow)
- [React 文档](https://react.dev)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
