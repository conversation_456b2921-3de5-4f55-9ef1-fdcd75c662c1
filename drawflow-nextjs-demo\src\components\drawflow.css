/* Drawflow 样式 */
@import 'drawflow/dist/drawflow.min.css';

/* 自定义节点样式 */
.drawflow .drawflow-node {
  background: white;
  border: 2px solid #4f46e5;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  min-height: 80px;
}

.drawflow .drawflow-node.selected {
  border-color: #7c3aed;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.drawflow .drawflow-node .title-box {
  background: #4f46e5;
  color: white;
  padding: 8px 12px;
  margin: -1px -1px 8px -1px;
  border-radius: 6px 6px 0 0;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
}

.drawflow .drawflow-node .box {
  padding: 12px;
  color: #374151;
  font-size: 13px;
  line-height: 1.4;
}

.drawflow .drawflow-node .box p {
  margin: 0;
  text-align: center;
}

/* 连接点样式 */
.drawflow .drawflow-node .input,
.drawflow .drawflow-node .output {
  background: #4f46e5;
  border: 2px solid white;
  width: 12px;
  height: 12px;
}

.drawflow .drawflow-node .input:hover,
.drawflow .drawflow-node .output:hover {
  background: #7c3aed;
  transform: scale(1.2);
}

/* 连接线样式 */
.drawflow svg .connection .main-path {
  stroke: #4f46e5;
  stroke-width: 3px;
  fill: none;
}

.drawflow svg .connection .main-path:hover {
  stroke: #7c3aed;
  stroke-width: 4px;
}

.drawflow svg .connection.selected .main-path {
  stroke: #7c3aed;
  stroke-width: 4px;
}

/* 背景网格 */
.drawflow {
  background-image: 
    radial-gradient(circle, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .drawflow .drawflow-node {
    min-width: 140px;
    min-height: 70px;
  }
  
  .drawflow .drawflow-node .title-box {
    font-size: 12px;
    padding: 6px 8px;
  }
  
  .drawflow .drawflow-node .box {
    padding: 8px;
    font-size: 12px;
  }
}
