'use client';

import dynamic from 'next/dynamic';

// 动态导入 DrawflowEditor 组件，禁用 SSR
const DrawflowEditor = dynamic(() => import('./DrawflowEditor'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96 bg-gray-50 border border-gray-300 rounded-lg">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Drawflow Editor...</p>
      </div>
    </div>
  )
});

const DrawflowDemo: React.FC = () => {
  return (
    <div className="w-full h-96">
      <DrawflowEditor className="h-full" />
    </div>
  );
};

export default DrawflowDemo;
