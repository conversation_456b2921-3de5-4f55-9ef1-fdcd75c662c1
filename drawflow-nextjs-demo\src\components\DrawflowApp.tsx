'use client';

import { useEffect, useRef, useState } from 'react';
import Drawflow from 'drawflow';
import Swal from 'sweetalert2';

const DrawflowApp: React.FC = () => {
  const drawflowRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<Drawflow | null>(null);
  const [selectedModule, setSelectedModule] = useState('Home');
  const [isLocked, setIsLocked] = useState(false);

  // 拖拽节点数据
  const dragNodes = [
    { id: 'facebook', icon: 'fab fa-facebook', label: 'Facebook' },
    { id: 'slack', icon: 'fab fa-slack', label: 'Slack receive message' },
    { id: 'github', icon: 'fab fa-github', label: 'Github Star' },
    { id: 'telegram', icon: 'fab fa-telegram', label: 'Telegram send message' },
    { id: 'aws', icon: 'fab fa-aws', label: 'AWS' },
    { id: 'log', icon: 'fas fa-file-signature', label: 'File Log' },
    { id: 'google', icon: 'fab fa-google-drive', label: 'Google Drive save' },
    { id: 'email', icon: 'fas fa-at', label: 'Email send' },
    { id: 'template', icon: 'fas fa-code', label: 'Template' },
    { id: 'multiple', icon: 'fas fa-code-branch', label: 'Multiple inputs/outputs' },
    { id: 'personalized', icon: 'fas fa-fill', label: 'Personalized' },
    { id: 'dbclick', icon: 'fas fa-mouse', label: 'DBClick!' },
  ];

  // 预设数据 - 完全复制原始数据
  const dataToImport = {
    "drawflow": {
      "Home": {
        "data": {
          "1": {
            "id": 1,
            "name": "welcome",
            "data": {},
            "class": "welcome",
            "html": `
    <div>
      <div class="title-box">👏 Welcome!!</div>
      <div class="box">
        <p>Simple flow library <b>demo</b>
        <a href="https://github.com/jerosoler/Drawflow" target="_blank">Drawflow</a> by <b>Jero Soler</b></p><br>

        <p>Multiple input / outputs<br>
           Data sync nodes<br>
           Import / export<br>
           Modules support<br>
           Simple use<br>
           Type: Fixed or Edit<br>
           Events: view console<br>
           Pure Javascript<br>
        </p>
        <br>
        <p><b><u>Shortkeys:</u></b></p>
        <p>🎹 <b>Delete</b> for remove selected<br>
        💠 Mouse Left Click == Move<br>
        ❌ Mouse Right == Delete Option<br>
        🔍 Ctrl + Wheel == Zoom<br>
        📱 Mobile support<br>
        ...</p>
      </div>
    </div>
    `,
            "typenode": false,
            "inputs": {},
            "outputs": {},
            "pos_x": 50,
            "pos_y": 50
          },
          "2": {
            "id": 2,
            "name": "slack",
            "data": {},
            "class": "slack",
            "html": `
          <div>
            <div class="title-box"><i class="fab fa-slack"></i> Slack chat message</div>
          </div>
          `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [{ "node": "7", "input": "output_1" }] } },
            "outputs": {},
            "pos_x": 1028,
            "pos_y": 87
          },
          "3": {
            "id": 3,
            "name": "telegram",
            "data": { "channel": "channel_2" },
            "class": "telegram",
            "html": `
          <div>
            <div class="title-box"><i class="fab fa-telegram-plane"></i> Telegram bot</div>
            <div class="box">
              <p>Send to telegram</p>
              <p>select channel</p>
              <select df-channel>
                <option value="channel_1">Channel 1</option>
                <option value="channel_2">Channel 2</option>
                <option value="channel_3">Channel 3</option>
                <option value="channel_4">Channel 4</option>
              </select>
            </div>
          </div>
          `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [{ "node": "7", "input": "output_1" }] } },
            "outputs": {},
            "pos_x": 1032,
            "pos_y": 184
          },
          "4": {
            "id": 4,
            "name": "email",
            "data": {},
            "class": "email",
            "html": `
            <div>
              <div class="title-box"><i class="fas fa-at"></i> Send Email </div>
            </div>
            `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [{ "node": "5", "input": "output_1" }] } },
            "outputs": {},
            "pos_x": 1033,
            "pos_y": 439
          },
          "5": {
            "id": 5,
            "name": "template",
            "data": { "template": "Write your template" },
            "class": "template",
            "html": `
            <div>
              <div class="title-box"><i class="fas fa-code"></i> Template</div>
              <div class="box">
                Ger Vars
                <textarea df-template></textarea>
                Output template with vars
              </div>
            </div>
            `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [{ "node": "6", "input": "output_1" }] } },
            "outputs": { "output_1": { "connections": [{ "node": "4", "output": "input_1" }, { "node": "11", "output": "input_1" }] } },
            "pos_x": 607,
            "pos_y": 304
          },
          "6": {
            "id": 6,
            "name": "github",
            "data": { "name": "https://github.com/jerosoler/Drawflow" },
            "class": "github",
            "html": `
          <div>
            <div class="title-box"><i class="fab fa-github "></i> Github Stars</div>
            <div class="box">
              <p>Enter repository url</p>
            <input type="text" df-name>
            </div>
          </div>
          `,
            "typenode": false,
            "inputs": {},
            "outputs": { "output_1": { "connections": [{ "node": "5", "output": "input_1" }] } },
            "pos_x": 341,
            "pos_y": 191
          },
          "7": {
            "id": 7,
            "name": "facebook",
            "data": {},
            "class": "facebook",
            "html": `
        <div>
          <div class="title-box"><i class="fab fa-facebook"></i> Facebook Message</div>
        </div>
        `,
            "typenode": false,
            "inputs": {},
            "outputs": { "output_1": { "connections": [{ "node": "2", "output": "input_1" }, { "node": "3", "output": "input_1" }, { "node": "11", "output": "input_1" }] } },
            "pos_x": 347,
            "pos_y": 87
          },
          "11": {
            "id": 11,
            "name": "log",
            "data": {},
            "class": "log",
            "html": `
            <div>
              <div class="title-box"><i class="fas fa-file-signature"></i> Save log file </div>
            </div>
            `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [{ "node": "5", "input": "output_1" }, { "node": "7", "input": "output_1" }] } },
            "outputs": {},
            "pos_x": 1031,
            "pos_y": 363
          }
        }
      },
      "Other": {
        "data": {
          "8": {
            "id": 8,
            "name": "personalized",
            "data": {},
            "class": "personalized",
            "html": `
            <div>
              Personalized
            </div>
            `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [{ "node": "12", "input": "output_1" }, { "node": "12", "input": "output_2" }, { "node": "12", "input": "output_3" }, { "node": "12", "input": "output_4" }] } },
            "outputs": { "output_1": { "connections": [{ "node": "9", "output": "input_1" }] } },
            "pos_x": 764,
            "pos_y": 227
          },
          "9": {
            "id": 9,
            "name": "dbclick",
            "data": { "name": "Hello World!!" },
            "class": "dbclick",
            "html": `
            <div>
            <div class="title-box"><i class="fas fa-mouse"></i> Db Click</div>
              <div class="box dbclickbox" ondblclick="showpopup(event)">
                Db Click here
                <div class="modal" style="display:none">
                  <div class="modal-content">
                    <span class="close" onclick="closemodal(event)">&times;</span>
                    Change your variable {name} !
                    <input type="text" df-name>
                  </div>

                </div>
              </div>
            </div>
            `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [{ "node": "8", "input": "output_1" }] } },
            "outputs": { "output_1": { "connections": [{ "node": "12", "output": "input_2" }] } },
            "pos_x": 209,
            "pos_y": 38
          },
          "12": {
            "id": 12,
            "name": "multiple",
            "data": {},
            "class": "multiple",
            "html": `
            <div>
              <div class="box">
                Multiple!
              </div>
            </div>
            `,
            "typenode": false,
            "inputs": { "input_1": { "connections": [] }, "input_2": { "connections": [{ "node": "9", "input": "output_1" }] }, "input_3": { "connections": [] } },
            "outputs": { "output_1": { "connections": [{ "node": "8", "output": "input_1" }] }, "output_2": { "connections": [{ "node": "8", "output": "input_1" }] }, "output_3": { "connections": [{ "node": "8", "output": "input_1" }] }, "output_4": { "connections": [{ "node": "8", "output": "input_1" }] } },
            "pos_x": 179,
            "pos_y": 272
          }
        }
      }
    }
  };

  // 拖拽处理函数
  const handleDragStart = (e: React.DragEvent, nodeType: string) => {
    e.dataTransfer.setData("node", nodeType);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const nodeType = e.dataTransfer.getData("node");
    addNodeToDrawFlow(nodeType, e.clientX, e.clientY);
  };

  // 添加节点到画布
  const addNodeToDrawFlow = (name: string, pos_x: number, pos_y: number) => {
    if (!editorRef.current || editorRef.current.editor_mode === 'fixed') {
      return false;
    }

    const editor = editorRef.current;

    // 计算相对位置
    pos_x = pos_x * (editor.precanvas.clientWidth / (editor.precanvas.clientWidth * editor.zoom)) - (editor.precanvas.getBoundingClientRect().x * (editor.precanvas.clientWidth / (editor.precanvas.clientWidth * editor.zoom)));
    pos_y = pos_y * (editor.precanvas.clientHeight / (editor.precanvas.clientHeight * editor.zoom)) - (editor.precanvas.getBoundingClientRect().y * (editor.precanvas.clientHeight / (editor.precanvas.clientHeight * editor.zoom)));

    // 根据节点类型创建不同的节点
    switch (name) {
      case 'facebook':
        const facebook = `
        <div>
          <div class="title-box"><i class="fab fa-facebook"></i> Facebook Message</div>
        </div>
        `;
        editor.addNode('facebook', 0, 1, pos_x, pos_y, 'facebook', {}, facebook);
        break;
      case 'slack':
        const slackchat = `
        <div>
          <div class="title-box"><i class="fab fa-slack"></i> Slack chat message</div>
        </div>
        `;
        editor.addNode('slack', 1, 0, pos_x, pos_y, 'slack', {}, slackchat);
        break;
      case 'github':
        const githubtemplate = `
        <div>
          <div class="title-box"><i class="fab fa-github "></i> Github Stars</div>
          <div class="box">
            <p>Enter repository url</p>
          <input type="text" df-name>
          </div>
        </div>
        `;
        editor.addNode('github', 0, 1, pos_x, pos_y, 'github', { "name": '' }, githubtemplate);
        break;
      case 'telegram':
        const telegrambot = `
        <div>
          <div class="title-box"><i class="fab fa-telegram-plane"></i> Telegram bot</div>
          <div class="box">
            <p>Send to telegram</p>
            <p>select channel</p>
            <select df-channel>
              <option value="channel_1">Channel 1</option>
              <option value="channel_2">Channel 2</option>
              <option value="channel_3">Channel 3</option>
              <option value="channel_4">Channel 4</option>
            </select>
          </div>
        </div>
        `;
        editor.addNode('telegram', 1, 0, pos_x, pos_y, 'telegram', { "channel": 'channel_3' }, telegrambot);
        break;
      case 'aws':
        const aws = `
        <div>
          <div class="title-box"><i class="fab fa-aws"></i> Aws Save </div>
          <div class="box">
            <p>Save in aws</p>
            <input type="text" df-db-dbname placeholder="DB name"><br><br>
            <input type="text" df-db-key placeholder="DB key">
            <p>Output Log</p>
          </div>
        </div>
        `;
        editor.addNode('aws', 1, 1, pos_x, pos_y, 'aws', { "db": { "dbname": '', "key": '' } }, aws);
        break;
      case 'log':
        const log = `
        <div>
          <div class="title-box"><i class="fas fa-file-signature"></i> Save log file </div>
        </div>
        `;
        editor.addNode('log', 1, 0, pos_x, pos_y, 'log', {}, log);
        break;
      case 'google':
        const google = `
        <div>
          <div class="title-box"><i class="fab fa-google-drive"></i> Google Drive save </div>
        </div>
        `;
        editor.addNode('google', 1, 0, pos_x, pos_y, 'google', {}, google);
        break;
      case 'email':
        const email = `
        <div>
          <div class="title-box"><i class="fas fa-at"></i> Send Email </div>
        </div>
        `;
        editor.addNode('email', 1, 0, pos_x, pos_y, 'email', {}, email);
        break;
      case 'template':
        const template = `
        <div>
          <div class="title-box"><i class="fas fa-code"></i> Template</div>
          <div class="box">
            Ger Vars
            <textarea df-template></textarea>
            Output template with vars
          </div>
        </div>
        `;
        editor.addNode('template', 1, 1, pos_x, pos_y, 'template', { "template": 'Write your template' }, template);
        break;
      case 'multiple':
        const multiple = `
        <div>
          <div class="box">
            Multiple!
          </div>
        </div>
        `;
        editor.addNode('multiple', 3, 4, pos_x, pos_y, 'multiple', {}, multiple);
        break;
      case 'personalized':
        const personalized = `
        <div>
          Personalized
        </div>
        `;
        editor.addNode('personalized', 1, 1, pos_x, pos_y, 'personalized', {}, personalized);
        break;
      case 'dbclick':
        const dbclick = `
        <div>
        <div class="title-box"><i class="fas fa-mouse"></i> Db Click</div>
          <div class="box dbclickbox">
            Db Click here
          </div>
        </div>
        `;
        editor.addNode('dbclick', 1, 1, pos_x, pos_y, 'dbclick', { name: '' }, dbclick);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (!drawflowRef.current) return;

    // 初始化 Drawflow
    const editor = new Drawflow(drawflowRef.current);
    editorRef.current = editor;

    // 设置配置
    editor.reroute = true;
    editor.reroute_fix_curvature = true;
    editor.force_first_input = false;

    editor.start();

    // 导入预设数据
    setTimeout(() => {
      try {
        editor.import(dataToImport);
      } catch (error) {
        console.warn('导入数据时出错:', error);
      }
    }, 100);

    // 添加事件监听器
    editor.on('nodeCreated', (id: number) => {
      console.log("Node created " + id);
    });

    editor.on('nodeRemoved', (id: number) => {
      console.log("Node removed " + id);
    });

    editor.on('nodeSelected', (id: number) => {
      console.log("Node selected " + id);
    });

    editor.on('moduleCreated', (name: string) => {
      console.log("Module Created " + name);
    });

    editor.on('moduleChanged', (name: string) => {
      console.log("Module Changed " + name);
    });

    editor.on('connectionCreated', (connection: any) => {
      console.log('Connection created');
      console.log(connection);
    });

    editor.on('connectionRemoved', (connection: any) => {
      console.log('Connection removed');
      console.log(connection);
    });

    editor.on('nodeMoved', (id: number) => {
      console.log("Node moved " + id);
    });

    editor.on('zoom', (zoom: number) => {
      console.log('Zoom level ' + zoom);
    });

    editor.on('translate', (position: { x: number, y: number }) => {
      console.log('Translate x:' + position.x + ' y:' + position.y);
    });

    editor.on('addReroute', (id: number) => {
      console.log("Reroute added " + id);
    });

    editor.on('removeReroute', (id: number) => {
      console.log("Reroute removed " + id);
    });

    return () => {
      if (editorRef.current) {
        try {
          editorRef.current.clear();
        } catch (error) {
          console.warn('清理 Drawflow 时出错:', error);
        }
      }
    };
  }, []);

  return (
    <>
      <header>
        <h2>Drawflow</h2>
        <div className="github-link">
          <a href="https://github.com/jerosoler/Drawflow" target="_blank">
            <i className="fab fa-github fa-3x"></i>
          </a>
        </div>
      </header>
      
      <div className="wrapper">
        {/* 左侧拖拽面板 */}
        <div className="col">
          {dragNodes.map((node) => (
            <div
              key={node.id}
              className="drag-drawflow"
              draggable
              data-node={node.id}
              onDragStart={(e) => handleDragStart(e, node.id)}
            >
              <i className={node.icon}></i>
              <span> {node.label}</span>
            </div>
          ))}
        </div>

        {/* 右侧编辑器 */}
        <div className="col-right">
          <div className="menu">
            <ul>
              <li 
                onClick={() => {
                  if (editorRef.current) {
                    editorRef.current.changeModule('Home');
                    setSelectedModule('Home');
                  }
                }}
                className={selectedModule === 'Home' ? 'selected' : ''}
              >
                Home
              </li>
              <li 
                onClick={() => {
                  if (editorRef.current) {
                    editorRef.current.changeModule('Other');
                    setSelectedModule('Other');
                  }
                }}
                className={selectedModule === 'Other' ? 'selected' : ''}
              >
                Other Module
              </li>
            </ul>
          </div>
          
          <div
            id="drawflow"
            ref={drawflowRef}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <div
              className="btn-export"
              onClick={() => {
                if (editorRef.current) {
                  Swal.fire({
                    title: 'Export',
                    html: '<pre><code>' + JSON.stringify(editorRef.current.export(), null, 4) + '</code></pre>',
                    width: '80%',
                    customClass: {
                      popup: 'swal-wide'
                    }
                  });
                }
              }}
            >
              Export
            </div>
            
            <div 
              className="btn-clear"
              onClick={() => {
                if (editorRef.current) {
                  editorRef.current.clearModuleSelected();
                }
              }}
            >
              Clear
            </div>
            
            <div className="btn-lock">
              {!isLocked ? (
                <i 
                  className="fas fa-lock"
                  onClick={() => {
                    if (editorRef.current) {
                      editorRef.current.editor_mode = 'fixed';
                      setIsLocked(true);
                    }
                  }}
                ></i>
              ) : (
                <i 
                  className="fas fa-lock-open"
                  onClick={() => {
                    if (editorRef.current) {
                      editorRef.current.editor_mode = 'edit';
                      setIsLocked(false);
                    }
                  }}
                ></i>
              )}
            </div>
            
            <div className="bar-zoom">
              <i 
                className="fas fa-search-minus"
                onClick={() => {
                  if (editorRef.current) {
                    editorRef.current.zoom_out();
                  }
                }}
              ></i>
              <i 
                className="fas fa-search"
                onClick={() => {
                  if (editorRef.current) {
                    editorRef.current.zoom_reset();
                  }
                }}
              ></i>
              <i 
                className="fas fa-search-plus"
                onClick={() => {
                  if (editorRef.current) {
                    editorRef.current.zoom_in();
                  }
                }}
              ></i>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DrawflowApp;
