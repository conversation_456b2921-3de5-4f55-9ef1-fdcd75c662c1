'use client';

import { useEffect, useRef } from 'react';
import Drawflow from 'drawflow';

export default function TestPage() {
  const drawflowRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!drawflowRef.current) return;

    const editor = new Drawflow(drawflowRef.current);
    editor.start();

    // 简单的节点
    const html = `
      <div style="padding: 10px; background: white; border: 1px solid #ccc; border-radius: 4px;">
        <h4>Test Node</h4>
        <p>This is a test</p>
      </div>
    `;

    editor.addNode('test', 1, 1, 200, 200, 'test', {}, html);

    return () => {
      editor.clear();
    };
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Drawflow Test Page</h1>
      <div 
        ref={drawflowRef} 
        className="w-full h-96 border border-gray-300 bg-gray-50"
      />
    </div>
  );
}
