// TypeScript 类型定义
declare class Drawflow {
  constructor(container: HTMLElement, render?: any, parent?: any);
  
  // 属性
  canvas_x: number;
  canvas_y: number;
  pos_x: number;
  pos_y: number;
  mouse_x: number;
  mouse_y: number;
  line_path: number;
  first_click: any;
  force_first_input: boolean;
  draggable_inputs: boolean;
  useuuid: boolean;
  parent: any;
  nodeId: number;
  ele_selected: any;
  node_selected: any;
  drag: boolean;
  reroute: boolean;
  reroute_fix_curvature: boolean;
  curvature: number;
  reroute_curvature_start_end: number;
  reroute_curvature: number;
  reroute_width: number;
  drag_point: boolean;
  editor_selected: boolean;
  connection: boolean;
  connection_ele: any;
  connection_selected: any;
  canvas_x_old: number;
  canvas_y_old: number;
  precanvas: any;
  noderegister: any;
  render: any;
  drawflow: any;
  module: string;
  editor_mode: string;
  zoom: number;
  zoom_max: number;
  zoom_min: number;
  zoom_value: number;
  zoom_last_value: number;

  // 方法
  start(): void;
  load(): void;
  removeReouteConnectionSelected(): void;
  click(e: Event): void;
  position(e: Event): void;
  drag_start(e: Event): void;
  drag_end(e: Event): void;
  zoom_enter(event: Event, delta: number): void;
  zoom_refresh(): void;
  zoom_in(): void;
  zoom_out(): void;
  zoom_reset(): void;
  createCurvature(start_pos_x: number, start_pos_y: number, end_pos_x: number, end_pos_y: number, curvature_value: number, type: string): string;
  drawConnection(ele: any): void;
  updateConnection(id: string): void;
  addConnection(id_output: number, id_input: number, output_class: string, input_class: string): boolean;
  removeSingleConnection(id_output: number, id_input: number, output_class: string, input_class: string): boolean;
  removeConnection(): boolean;
  removeConnectionNodeId(id: string): void;
  removeNodeId(id: string): void;
  removeNode(id: string): boolean;
  addNode(name: string, num_in: number, num_out: number, ele_pos_x: number, ele_pos_y: number, classoverride: string, data: any, html: string, typenode?: boolean): number;
  addNodeImport(dataNode: any, precanvas: any): void;
  addRerouteImport(dataNode: any): void;
  updateNodeValue(event: Event): void;
  updateNodeDataFromId(id: number, data: any): void;
  addInputToNode(id: number): void;
  addOutputToNode(id: number): void;
  removeInputToNode(id: number): void;
  removeOutputToNode(id: number): void;
  removeNodeInputs(id: number): void;
  removeNodeOutputs(id: number): void;
  getModuleFromNodeId(id: number): string;
  addModule(name: string): void;
  changeModule(name: string): void;
  removeModule(name: string): void;
  clearModuleSelected(): void;
  clear(): void;
  export(): any;
  import(data: any): void;
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
  dispatch(event: string, details?: any): void;
  getNodeFromId(id: number): any;
  getNodesFromName(name: string): any[];
  addNodeInput(id: number): void;
  addNodeOutput(id: number): void;
  removeNodeInput(id: number, input_class: string): void;
  removeNodeOutput(id: number, output_class: string): void;
}

export default Drawflow;
